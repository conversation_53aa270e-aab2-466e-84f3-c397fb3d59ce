import { PayrixService } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

export interface UserAccountResult {
  success: boolean;
  data?: {
    id: string;
    email: string;
    sanitizedUsername?: string;
    originalUsername?: string;
    [key: string]: unknown;
  };
  error?: string;
}

export async function createUserAccountIfRequested(data: OnboardingRequest, payrixEntityId: string, requestId: string): Promise<UserAccountResult> {
  if (!data.userAccount?.createAccount || !data.userAccount?.username || !data.userAccount?.password) {
    return { success: false, error: "Username and password are required for account creation" };
  }

  try {
    logger.info("Creating user account for merchant", {
      requestId,
      payrixEntityId,
      username: data.userAccount.username,
    });

    const primaryMember = data.members.find((member) => member.primary === 1) || data.members[0];

    if (!primaryMember) {
      throw new Error("No primary member found to create user account");
    }

    const payrixService = new PayrixService();
    const userAccountData = await payrixService.createUserAccount({
      username: data.userAccount.username,
      password: data.userAccount.password,
      first: primaryMember.first,
      last: primaryMember.last,
      email: primaryMember.email,
      merchantId: payrixEntityId,
    });

    logger.info("User account created successfully", {
      requestId,
      payrixEntityId,
      username: data.userAccount.username,
      userAccountId: userAccountData?.id,
    });

    return {
      success: true,
      data: userAccountData,
    };
  } catch (userError) {
    logger.error("User account creation failed", {
      requestId,
      payrixEntityId,
      username: data.userAccount?.username,
      error: (userError as Error).message,
    });

    return {
      success: false,
      error: (userError as Error).message,
    };
  }
}
