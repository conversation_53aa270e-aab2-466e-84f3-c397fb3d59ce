import { z } from "zod";
import { phoneSchema, emailSchema, einSchema, zipSchema, mccSchema, tcDateSchema } from "./validation-helpers.schema.js";
import { accountsSchema } from "./account.schema.js";
import { memberSchema } from "./member.schema.js";

const businessTypeSchema = z.union([
  z.literal("partnership"),
  z.literal("associationCorporation"),
  z.literal("governmentOrganization"),
  z.literal("taxExemptOrganization"),
  z.literal("internationalOrganization"),
  z.literal("soleProprietorship"),
  z.literal("limitedLiabilityCompany"),
  z.literal("publicCorporation"),
  z.literal("privateCorporation"),
  z.literal("notForProfitCorporation"),
]);

const entityTypeSchema = z.union([
  z.literal("tenant"),
  z.literal("merchant"),
  z.literal("partner"),
  z.literal("referrer"),
  z.literal("reseller"),
  z.literal("isoAgent"),
  z.literal("iso"),
  z.literal("facilitator"),
  z.literal("referrerPartner"),
  z.literal("resellerPartner"),
]);

const bankVerificationFileSchema = z.object({
  name: z.string().min(1, "File name is required"),
  size: z.number().positive("File size must be positive"),
  type: z.string().min(1, "File type is required"),
  content: z.string().min(1, "File content is required"),
});

const plaidDataSchema = z.object({
  publicToken: z.string().min(1, "Public token is required"),
  accountToken: z.string().min(1, "Account token is required"),
  institutionName: z.string().optional(),
  accountMask: z.string().optional(),
});

const bankVerificationSchema = z.object({
  verificationMethod: z.enum(["manual", "plaid"]),
  verificationFile: bankVerificationFileSchema.optional(),
  plaidData: plaidDataSchema.optional(),
});

const userAccountSchema = z.object({
  createAccount: z.boolean(),
  username: z.string().optional(),
  password: z.string().optional(),
  confirmPassword: z.string().optional(),
});

const onboardingSchema = z.object({
  name: z.string().min(1, "Business legal name is required"),
  dba: z.string().optional(),
  businessType: businessTypeSchema,
  type: entityTypeSchema.default("merchant"),
  ein: einSchema,
  address1: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().length(2, "State must be 2 characters"),
  zip: zipSchema,
  country: z.string().length(3, "Country must be 3 characters").default("USA"),
  phone: phoneSchema,
  email: emailSchema,
  website: z.string().url("Invalid website URL").optional(),
  cashdiscount: z.number().int().min(0).max(1).default(0),
  mcc: mccSchema,
  accounts: accountsSchema.optional(),
  members: z.array(memberSchema).min(1, "At least one member is required"),
  tcDate: tcDateSchema,
  tcVersion: z.string().min(1, "Terms version is required"),
  tcIp: z.string().optional(),
  clientIp: z.string().optional(),
  credentialId: z.string().optional(),
  partner: z.string().optional(),
  estimated: z.string().optional(),
  annualProcessing: z.string().optional(),
  monthlyProcessing: z.string().optional(),
  averageTicket: z.string().optional(),
  maxTransactionAmount: z.string().optional(),
  referrer: z.string().optional(),
  referrerEntity: z.string().optional(),
  bankVerification: bankVerificationSchema.optional(),
  userAccount: userAccountSchema.optional(),
});

export type OnboardingRequest = z.infer<typeof onboardingSchema>;

export function validateOnboardingRequest(data: unknown): {
  success: boolean;
  data?: OnboardingRequest;
  errors?: string[];
} {
  const result = onboardingSchema.safeParse(data);

  if (!result.success) {
    const errors = result.error.errors.map((err) => {
      const path = err.path.join(".");
      return path ? `${path}: ${err.message}` : err.message;
    });

    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: result.data,
  };
}
