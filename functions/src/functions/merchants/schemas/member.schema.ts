import { z } from "zod";
import { emailSchema, dobSchema, phoneSchema, ssnSchema, percentageSchema } from "./validation-helpers.schema.js";

export const memberSchema = z.object({
  first: z.string().min(1, "First name is required"),
  last: z.string().min(1, "Last name is required"),
  email: emailSchema,
  dob: dobSchema,
  title: z.string().min(1, "Business title is required"),
  phone: phoneSchema,
  address1: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().length(2, "State must be 2 characters"),
  zip: z.string().min(5, "ZIP code is required"),
  ssn: ssnSchema,
  owner: z.number().int().min(0).max(1, "Owner must be 0 or 1"),
  primary: z.number().int().min(0).max(1, "Primary must be 0 or 1"),
  ownershipPercent: percentageSchema.optional(),
});